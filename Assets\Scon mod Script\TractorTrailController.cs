using UnityEngine;

public class TractorTrailController : MonoBehaviour
{
    [Header("Tractor References")]
    public TrailSplinePainter trailPainter;
    public RCC_CarControllerV3 carController;
    
    [Header("Trail Control Settings")]
    public KeyCode toggleTrailKey = KeyCode.T;
    public KeyCode clearTrailKey = KeyCode.C;
    public KeyCode paintTrailKey = KeyCode.P;
    public float minSpeedForTrail = 2f;
    public bool autoStartTrail = true;
    
    [Header("Trail Behavior")]
    public bool adaptToSpeed = true;
    public float minTrailWidth = 1.5f;
    public float maxTrailWidth = 4f;
    public float speedWidthMultiplier = 0.1f;
    
    [Header("UI Feedback")]
    public UnityEngine.UI.Text statusText;
    public UnityEngine.UI.Button toggleButton;
    public UnityEngine.UI.Button clearButton;
    
    private bool isTrailActive = false;
    private float currentSpeed = 0f;
    
    void Start()
    {
        // Get components if not assigned
        if (trailPainter == null)
            trailPainter = GetComponent<TrailSplinePainter>();
            
        if (carController == null)
            carController = GetComponent<RCC_CarControllerV3>();
        
        // Setup UI buttons
        if (toggleButton != null)
            toggleButton.onClick.AddListener(ToggleTrail);
            
        if (clearButton != null)
            clearButton.onClick.AddListener(ClearTrail);
        
        // Auto-start trail if enabled
        if (autoStartTrail && trailPainter != null)
        {
            isTrailActive = true;
            trailPainter.paintRealtime = true;
        }
        
        UpdateUI();
    }
    
    void Update()
    {
        // Get current speed
        if (carController != null)
        {
            currentSpeed = carController.speed;
        }
        else
        {
            // Fallback to rigidbody velocity
            Rigidbody rb = GetComponent<Rigidbody>();
            if (rb != null)
                currentSpeed = rb.velocity.magnitude * 3.6f; // Convert to km/h
        }
        
        // Handle input
        HandleInput();
        
        // Adapt trail width to speed
        if (adaptToSpeed && trailPainter != null && isTrailActive)
        {
            AdaptTrailToSpeed();
        }
        
        // Control trail painting based on speed
        if (trailPainter != null)
        {
            bool shouldPaint = isTrailActive && currentSpeed >= minSpeedForTrail;
            trailPainter.paintRealtime = shouldPaint;
        }
        
        UpdateUI();
    }
    
    void HandleInput()
    {
        if (Input.GetKeyDown(toggleTrailKey))
        {
            ToggleTrail();
        }
        
        if (Input.GetKeyDown(clearTrailKey))
        {
            ClearTrail();
        }
        
        if (Input.GetKeyDown(paintTrailKey))
        {
            PaintCurrentTrail();
        }
    }
    
    void AdaptTrailToSpeed()
    {
        // Adjust trail width based on speed
        float speedFactor = Mathf.Clamp01(currentSpeed * speedWidthMultiplier / 10f);
        float adaptedWidth = Mathf.Lerp(minTrailWidth, maxTrailWidth, speedFactor);
        trailPainter.trailWidth = adaptedWidth;
        
        // Adjust resolution based on speed (higher speed = more points)
        float adaptedResolution = Mathf.Lerp(0.8f, 0.3f, speedFactor);
        trailPainter.trailResolution = adaptedResolution;
    }
    
    public void ToggleTrail()
    {
        isTrailActive = !isTrailActive;
        
        if (trailPainter != null)
        {
            trailPainter.paintRealtime = isTrailActive && currentSpeed >= minSpeedForTrail;
        }
        
        Debug.Log($"Trail painting: {(isTrailActive ? "ON" : "OFF")}");
    }
    
    public void ClearTrail()
    {
        if (trailPainter != null)
        {
            trailPainter.ClearTrail();
        }
        
        Debug.Log("Trail cleared!");
    }
    
    public void PaintCurrentTrail()
    {
        if (trailPainter != null)
        {
            trailPainter.ForcePaintTrail();
        }
        
        Debug.Log("Trail painted on terrain!");
    }
    
    void UpdateUI()
    {
        if (statusText != null)
        {
            string status = isTrailActive ? "ON" : "OFF";
            string speedStatus = currentSpeed >= minSpeedForTrail ? "PAINTING" : "WAITING";
            statusText.text = $"Trail: {status} | Speed: {currentSpeed:F1} km/h | Status: {speedStatus}";
        }
        
        if (toggleButton != null)
        {
            var buttonText = toggleButton.GetComponentInChildren<UnityEngine.UI.Text>();
            if (buttonText != null)
            {
                buttonText.text = isTrailActive ? "Stop Trail" : "Start Trail";
            }
        }
    }
    
    // Public methods for external control
    public void SetTrailActive(bool active)
    {
        isTrailActive = active;
        if (trailPainter != null)
        {
            trailPainter.paintRealtime = active && currentSpeed >= minSpeedForTrail;
        }
    }
    
    public void SetTrailWidth(float width)
    {
        if (trailPainter != null)
        {
            trailPainter.trailWidth = width;
        }
    }
    
    public void SetTrailIntensity(float intensity)
    {
        if (trailPainter != null)
        {
            trailPainter.paintIntensity = intensity;
        }
    }
    
    public bool IsTrailActive()
    {
        return isTrailActive;
    }
    
    public float GetCurrentSpeed()
    {
        return currentSpeed;
    }
    
    void OnGUI()
    {
        if (!Application.isPlaying) return;
        
        // Simple debug GUI
        GUILayout.BeginArea(new Rect(10, 10, 300, 150));
        GUILayout.Label($"Trail System Status:");
        GUILayout.Label($"Active: {isTrailActive}");
        GUILayout.Label($"Speed: {currentSpeed:F1} km/h");
        GUILayout.Label($"Painting: {(trailPainter != null && trailPainter.paintRealtime)}");
        
        if (GUILayout.Button("Toggle Trail (T)"))
        {
            ToggleTrail();
        }
        
        if (GUILayout.Button("Clear Trail (C)"))
        {
            ClearTrail();
        }
        
        if (GUILayout.Button("Paint Trail (P)"))
        {
            PaintCurrentTrail();
        }
        
        GUILayout.EndArea();
    }
}
