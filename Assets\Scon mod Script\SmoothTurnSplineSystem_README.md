# Smooth Turn Spline System for TerrainPainter
## Like Farming Simulator 2025 - Seamless Texture Transitions

This enhanced TerrainPainter now includes a sophisticated smooth turn spline system that creates seamless texture transitions during vehicle turns, similar to the advanced terrain painting system in Farming Simulator 2025.

## Key Features

### 🌟 Smooth Turn Spline System
- **Catmull-Rom Spline Interpolation**: Creates perfectly smooth curves between movement points
- **Real-time Turn Detection**: Automatically detects when the vehicle is turning
- **Adaptive Trail Points**: Dynamically adjusts trail resolution based on movement
- **Configurable Smoothing**: Multiple smoothing iterations for ultra-smooth results

### 🎨 Seamless Texture Blending
- **Edge Feathering**: Soft edges that blend naturally with existing terrain
- **Transition Smoothness**: Configurable smoothness for perfect texture transitions
- **Turn-Based Adaptation**: Brush width and intensity adapt to turn angles
- **Anti-Aliasing**: Smooth falloff prevents harsh texture boundaries

### ⚙️ Advanced Configuration

#### Spline Settings
- `useSmoothTurnSpline`: Enable/disable the spline system
- `splineResolution`: How often to create trail points (lower = smoother)
- `smoothingFactor`: Amount of smoothing applied (0-1)
- `smoothingIterations`: Number of smoothing passes
- `turnDetectionAngle`: Minimum angle to detect a turn

#### Turn-Based Blending
- `adaptiveWidthOnTurns`: Adjust brush width during turns
- `minTurnWidth` / `maxTurnWidth`: Width range for turns
- `turnWidthCurve`: Custom curve for width adaptation
- `turnIntensityCurve`: Custom curve for intensity adaptation

#### Seamless Transitions
- `useSeamlessTransition`: Enable seamless texture blending
- `transitionSmoothness`: How smooth the transitions are
- `edgeFeathering`: Feathering multiplier for soft edges

## How It Works

### 1. Trail Point Collection
The system continuously collects trail points as the vehicle moves, storing:
- Position
- Direction
- Width (adaptive based on turns)
- Intensity (adaptive based on turns)
- Turn angle
- Timestamp

### 2. Spline Generation
- Applies multiple smoothing iterations to raw trail points
- Uses Catmull-Rom spline interpolation for smooth curves
- Generates high-resolution interpolated points along the spline

### 3. Seamless Painting
- Paints along the smoothed spline instead of raw positions
- Uses distance-based falloff with SmoothStep for natural blending
- Applies edge feathering for seamless texture transitions
- Adapts brush parameters based on turn characteristics

## Usage Instructions

### Basic Setup
1. Add the TerrainPainter component to your vehicle
2. Enable "Use Smooth Turn Spline" in the inspector
3. Configure the spline and blending parameters
4. Set your target and apply texture layers

### Recommended Settings for Farming Simulator-like Results
```
Smooth Turn Spline System:
- Use Smooth Turn Spline: ✓
- Spline Resolution: 0.3
- Smoothing Factor: 0.4
- Smoothing Iterations: 2
- Turn Detection Angle: 10°

Turn-Based Texture Blending:
- Adaptive Width On Turns: ✓
- Min Turn Width: 1.5
- Max Turn Width: 4.0
- Max Turn Angle: 45°

Seamless Texture Transition:
- Use Seamless Transition: ✓
- Transition Smoothness: 0.8
- Edge Feathering: 1.2
```

### Context Menu Commands
- **Clear Spline Trail**: Clears all trail points and spline data
- **Toggle Smooth Turn Spline**: Enables/disables the spline system
- **Test Spline System**: Shows detailed status information

### Debug Visualization
Enable "Show Debug Gizmos" to see:
- Trail points (cyan/yellow spheres)
- Direction arrows (magenta)
- Smoothed spline (green lines)
- Turn indicators (red sphere when turning)
- Turn angle display (in Scene view)

## Performance Notes

- The system automatically cleans old trail points to maintain performance
- Spline generation is optimized for real-time use
- Trail length is limited to prevent memory issues
- Smoothing iterations can be adjusted for performance vs. quality balance

## Comparison with Standard System

### Standard TerrainPainter
- Paints directly at current position
- Sharp corners and edges
- No turn adaptation
- Basic circular/square brushes

### Smooth Turn Spline System
- Paints along smoothed spline curves
- Seamless texture transitions
- Adaptive brush parameters
- Professional-grade results like Farming Simulator 2025

## Tips for Best Results

1. **Lower spline resolution** for smoother curves (0.2-0.4)
2. **Higher smoothing factor** for more aggressive smoothing (0.3-0.5)
3. **Enable adaptive width** for realistic turn behavior
4. **Use seamless transitions** for professional results
5. **Adjust edge feathering** based on terrain texture scale
6. **Test different turn detection angles** for your vehicle's handling

This system transforms basic terrain painting into a sophisticated, professional-grade texture application system that rivals commercial farming simulators!
